# 粒子特效抽奖系统

## 概述

这是对原有抽奖程序的增强版本，将原来的canvas老虎机动画替换为更加炫酷的JS粒子特效动画。

## 新增功能

### 1. 粒子特效动画
- **粒子爆炸效果**: 抽奖开始时的粒子流动画
- **烟花庆祝效果**: 中奖时的多彩烟花动画
- **数字滚动动画**: 平滑的数字滚动效果，带有脉冲和缩放动画
- **实时粒子系统**: 基于物理引擎的粒子运动，包含重力、碰撞等效果

### 2. 改进的用户界面
- **现代化设计**: 使用半透明背景和阴影效果
- **响应式布局**: 适配不同屏幕尺寸
- **动画反馈**: 按钮点击和状态变化都有相应的视觉反馈
- **状态指示**: 清晰的状态提示文字

### 3. 增强的交互体验
- **两阶段抽奖**: 按Enter开始，再按Enter停止
- **延迟停止效果**: 数字逐个停止，增加悬念
- **庆祝动画**: 中奖后自动播放庆祝特效
- **重置功能**: 按Escape键快速重置

## 文件结构

```
├── particle-lottery.html          # 新的粒子特效抽奖页面
├── js/particle-effects.js         # 粒子特效系统
├── slotmachines.html              # 原有的老虎机抽奖页面
├── setting.html                   # 设置页面（已更新，添加新页面链接）
└── js/
    ├── index.js                   # 后端服务
    ├── modal.js                   # 弹窗组件
    └── jquery-3.7.1.min.js       # jQuery库
```

## 使用方法

### 1. 启动服务
```bash
node js/index.js
```

### 2. 设置抽奖数据
1. 打开 `setting.html`
2. 设置奖项信息
3. 点击"重置"按钮从Excel导入参与人员
4. 点击"去抽奖(粒子特效)"进入新的抽奖页面

### 3. 抽奖操作
- **Enter/PageDown**: 开始抽奖或停止抽奖
- **x**: 切换全屏模式
- **Alt**: 切换调试模式（固定抽取第一个人）
- **Escape**: 重置当前抽奖状态

## 技术特点

### 粒子系统
- **Particle类**: 基础粒子，支持重力、碰撞、生命周期
- **FireworkParticle类**: 烟花粒子，专门用于庆祝效果
- **ParticleSystem类**: 粒子系统管理器，处理所有粒子的创建和动画

### 动画效果
- **数字滚动**: 使用NumberRoller类实现平滑的数字变化
- **CSS动画**: 脉冲、缩放、淡入淡出等效果
- **Canvas动画**: 60fps的粒子动画循环

### 性能优化
- **粒子生命周期管理**: 自动清理过期粒子
- **requestAnimationFrame**: 使用浏览器优化的动画循环
- **Canvas离屏渲染**: 提高复杂图形的渲染性能

## 兼容性

- **浏览器支持**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **移动端**: 支持触摸设备，响应式设计
- **原有功能**: 完全兼容原有的数据格式和存储方式

## 自定义配置

### 粒子效果参数
可以在 `particle-effects.js` 中调整以下参数：
- 粒子数量
- 颜色方案
- 动画速度
- 重力效果
- 生命周期

### 动画时间
可以在 `particle-lottery.html` 中调整：
- 数字滚动速度
- 停止延迟时间
- 庆祝动画持续时间

## 对比原版的优势

1. **视觉效果更佳**: 粒子特效比老虎机滚动更加炫酷
2. **用户体验更好**: 两阶段操作增加了互动性和悬念
3. **性能更优**: 使用现代Web技术，动画更流畅
4. **扩展性更强**: 模块化设计，易于添加新的特效
5. **兼容性保持**: 不影响原有功能，可以并存使用

## 注意事项

1. 确保浏览器支持Canvas和现代JavaScript特性
2. 建议在性能较好的设备上运行以获得最佳体验
3. 粒子特效可能消耗更多GPU资源
4. 保持原有的数据备份，新版本完全兼容原有数据格式
