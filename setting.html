<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖项设置页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
            display: grid;
            justify-content: center;
        }

        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
        }

        .buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .buttons span {
            transform: translate(0, 20%);
        }

        .buttons button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: #fff;
            cursor: pointer;
        }

        .buttons input[type="number"] {
            margin-left: 10px;
            padding: 5px;
            border: none;
            border-radius: 5px;
            width: 60px;
            text-align: center;
        }

        .award-list,
        .participant-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .award-list li,
        .participant-list li {
            display: flex;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }

        .award-list li:last-child,
        .participant-list li:last-child {
            border-bottom: none;
        }

        .award-list .award-details,
        .participant-list .participant-details {
            display: flex;
            gap: 10px;
            width: 500px;
        }

        .award-list .award-details input,
        .participant-list .participant-details input {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        .award-list .award-operations,
        .participant-list .participant-operations {
            display: flex;
            gap: 5px;
            margin-left: 50px;
        }

        .award-list .award-operations button,
        .participant-list .participant-operations button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            min-width: 80px;
            flex-shrink: 0;
        }

        input[type="text"],
        input[type="number"] {
            border: none;
        }

        .participant-header {
            display: flex;
            background-color: #f1f1f1;
            font-weight: bold;
            justify-content: space-between;
        }

        .lonzaUser .num {
            width: 20px;
        }

        .lonzaUser li {
            list-style: none;
            margin: 5px;
        }

        .lonzaUser li input {
            width: 120px;
        }

        .lonzaUser .second {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .lonzaUser button {
            margin-left: auto;
        }



        .add {
            margin-left: 25px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">奖项设置（请按抽奖顺序添加）</div>
        <div class="buttons">
            <span>每个奖项每次抽取人数:</span>
            <input type="number" value="1" min="1">
            <button class="reset">重置中奖结果</button>
            <button class="lucky">去抽奖(老虎机)</button>
            <button class="particle-lucky">去抽奖(粒子特效)</button>
        </div>
        <ul class="award-list">

        </ul>
        <li class="header">参与人员列表（共0人）</li>
        <ul class="participant-list">

        </ul>
        <button id="addParticipant">增加</button>
        <button id="getUsers">重置</button>
    </div>
    <script src="./js/jquery-3.7.1.min.js"></script>
    <script>
        let getUsers = document.querySelector('#getUsers')
        const participantList = document.querySelector('.participant-list')
        const awardList = document.querySelector('.award-list')
        const awards = localStorage.getItem('awardInfo') ? JSON.parse(localStorage.getItem('awardInfo')) : []

        function AwardInfo(id, name, quantity, award) {
            this.id = id
            this.name = name
            this.quantity = quantity
            this.award = award
        }
        function getAwardInfo() {
            if (awards.length === 0) {
                awards.push(new AwardInfo(0, '一等奖', 1, '500京东卡'))
                localStorage.setItem('awardInfo', JSON.stringify(awards))
            }
            return awards
        }

        renderAward()
        renderUser()

        function renderAward() {
            awardList.innerHTML = ''
            getAwardInfo().forEach((item, index) => {
                const { name, award, quantity } = item
                const li = document.createElement('li')
                li.innerHTML = `
            <div class="award-details">
                <input type="text" data-role="name" value="${name}" placeholder="请输入奖项名称" data-id="${index}">
                <input type="number" data-role="quantity" value="${quantity}" min="1" placeholder="请输入数量" data-id="${index}">
                <input type="text" data-role="award" value="${award}" placeholder="请输入奖品详情" data-id="${index}">
            </div>
            <div class="award-operations">
                <button class='add'>增加</button>
                <button class='delete' data-id=${index}>删除</button>
            </div>
        `
                awardList.appendChild(li)
                li.querySelectorAll('input').forEach(input => {
                    input.addEventListener('input', function () {
                        const index = this.getAttribute('data-id')
                        const role = this.getAttribute('data-role')
                        const value = this.value
                        awards[index][role] = value
                        localStorage.setItem('awardInfo', JSON.stringify(awards))
                    })
                })
            })
        }

        document.querySelector('.buttons').addEventListener('click', (e) => {
            if (e.target.tagName == 'BUTTON') {
                if (e.target.classList.contains('reset')) {
                    localStorage.removeItem('user')
                    alert('Remove Success')
                } else if (e.target.classList.contains('lucky')) {
                    window.location.href = './slotmachines.html'
                } else if (e.target.classList.contains('particle-lucky')) {
                    window.location.href = './particle-lottery.html'
                }
            }
        })

        awardList.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON') {
                const btn = e.target;
                const awardDetails = btn.closest('li').querySelector('.award-details')
                if (btn.classList.contains('add')) {
                    const nameInput = awardDetails.querySelector('[data-role="name"]')
                    const quantityInput = awardDetails.querySelector('[data-role="quantity"]')
                    const awardInput = awardDetails.querySelector('[data-role="award"]')

                    if (nameInput && quantityInput && awardInput) {
                        const newAward = {
                            id: getAwardInfo().length,
                            name: nameInput.value,
                            quantity: quantityInput.value,
                            award: awardInput.value
                        }
                        awards.push(newAward)
                        localStorage.setItem('awardInfo', JSON.stringify(awards))
                        renderAward()
                    }
                } else if (btn.classList.contains('delete')) {
                    const index = btn.dataset.id
                    const awards = getAwardInfo()
                    awards.splice(index, 1)
                    localStorage.setItem('awardInfo', JSON.stringify(awards))
                    renderAward()
                }

            }
        })

        function getLuckyDogs() {
            return luckyDogs = localStorage.getItem('luckyDogs') ? JSON.parse(localStorage.getItem('luckyDogs')) : []
        }



        participantList.addEventListener('click', (e) => {
            const luckyDogs = getLuckyDogs()
            if (e.target.tagName === 'BUTTON') {
                luckyDogs.splice(e.target.dataset.id, 1)
                localStorage.setItem('luckyDogs', JSON.stringify(luckyDogs))
                renderUser()
            }
        })

        getUsers.addEventListener('click', () => {
            $.ajax({
                type: 'post',
                url: 'http://localhost:8080',
                data: { name: 'cc' },
                dataType: 'json',
                success: (res) => {
                    localStorage.setItem('luckyDogs', JSON.stringify(res))
                    renderUser()
                }
            })
        })



        function renderUser() {
            let num = 1
            participantList.innerHTML = ''
            getLuckyDogs().forEach((item, index) => {
                const { id, code, name, department } = item
                const li = document.createElement('li')
                li.className = 'lonzaUser'
                li.innerHTML = `
            <li class="num">${num++}</li>
            <li><input type="text" data-role="code" value="${code}" placeholder="请输入工号" data-id="${index}"></li>
            <li><input type="text" data-role="name" value="${name}" placeholder="请输入姓名" data-id="${index}"></li>
            <li><input type="text" data-role="department" value="${department}" placeholder="请输入部门" data-id="${index}"></li>
            <li><button data-id="${index}">删除</button></li>
        `
                participantList.appendChild(li)
                li.querySelectorAll('input').forEach(input => {
                    input.addEventListener('input', function () {
                        const index = this.getAttribute('data-id')
                        const role = this.getAttribute('data-role')
                        const value = this.value
                        luckyDogs[index][role] = value
                        localStorage.setItem('luckyDogs', JSON.stringify(luckyDogs))
                    })
                })
            })
        }
    </script>
</body>

</html>