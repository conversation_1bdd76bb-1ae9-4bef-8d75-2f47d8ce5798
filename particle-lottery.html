<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="./css/modal.css">
    <title>Lonza Lucky Draw - Particle Effects</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: grid;
            background-image: url('./images/drawbg.jpg');
            background-size: cover;
        }

        .main {
            width: 100%;
            position: relative;
        }

        .center {
            width: 100%;
            display: grid;
            justify-items: center;
        }

        .center .top {
            text-align: center;
            margin-top: 5vh;
            z-index: 10;
            position: relative;
        }

        .center .top ul {
            list-style: none;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-left: 100px;
        }

        .center .top h1 {
            font-size: 7vw;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .center .top h2 {
            margin: 10px;
            font-size: 2vw;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .center .top h3 {
            margin-bottom: 15px;
            font-size: 1.2vw;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        #particle-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .lottery-display {
            position: relative;
            z-index: 10;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            min-width: 400px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .number-display {
            font-size: 4rem;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            letter-spacing: 10px;
            text-align: center;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .winner-info {
            font-size: 1.5rem;
            color: #333;
            text-align: center;
            margin-top: 20px;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .winner-info.show {
            opacity: 1;
        }

        .draw ul {
            list-style: none;
            font-size: 3vw;
            color: #fff;
            text-align: center;
            z-index: 10;
            position: relative;
        }

        .awardInfo {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            z-index: 1000;
        }

        .awardInfo li {
            list-style: none;
            text-align: center;
            width: 1.5vw;
            margin: 2px;
            border-radius: 2px;
            border: 1px solid;
            background-color: #E0E0E0;
            cursor: pointer;
        }

        .awardInfo li:hover {
            cursor: pointer;
        }

        .status-text {
            font-size: 1.2rem;
            color: #666;
            margin: 10px 0;
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .firework {
            animation: firework 0.5s ease-out;
        }

        @keyframes firework {
            0% {
                transform: scale(0.5) rotate(0deg);
                opacity: 0;
            }

            50% {
                transform: scale(1.2) rotate(180deg);
                opacity: 1;
            }

            100% {
                transform: scale(1) rotate(360deg);
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <canvas id="particle-canvas"></canvas>
    <div class="main">
        <div class="center">
            <div class="top">
                <ul>
                    <li>
                        <h1></h1>
                    </li>
                    <li>
                        <h2></h2>
                    </li>
                    <li>
                        <h3></h3>
                    </li>
                </ul>
                <div class="lottery-display">
                    <div class="status-text">按 Enter 开始抽奖</div>
                    <div class="number-display" id="number-display">------</div>
                    <div class="winner-info" id="winner-info"></div>
                </div>
            </div>
            <div class="draw">
                <ul>
                    <!-- 抽奖结果将显示在这里 -->
                </ul>
            </div>
        </div>
        <div class="awardInfo">
            <!-- 奖项信息将显示在这里 -->
        </div>
    </div>

    <script src="./js/modal.js"></script>
    <script src="./js/jquery-3.7.1.min.js"></script>
    <script src="./js/particle-effects.js"></script>
    <script>
        let controlScreen = true
        let controlGame = true
        let drawCode = ''
        let getDog = false
        let isDrawing = false
        let currentNumbers = ['0', '0', '0', '0', '0', '0']
        let animationSpeed = 50
        let particleSystem

        // 初始化粒子系统
        window.addEventListener('load', function () {
            particleSystem = new ParticleSystem('particle-canvas')
        })

        document.addEventListener('keyup', (e) => {
            if (e.key === 'x') {
                if (controlScreen) {
                    document.documentElement.requestFullscreen()
                    controlScreen = false
                } else {
                    document.exitFullscreen()
                    controlScreen = true
                }
            }
            if (e.key === 'Alt') {
                if (!getDog) {
                    getDog = true
                } else {
                    getDog = false
                }
            }
        })

        const luckyDogs = localStorage.getItem('luckyDogs') ? JSON.parse(localStorage.getItem('luckyDogs')) : []
        const awardInfo = localStorage.getItem('awardInfo') ? JSON.parse(localStorage.getItem('awardInfo')) : []
        const userArr = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : []

        if (userArr.length === 0) {
            for (let i = 0; i < awardInfo.length; i++) {
                userArr[i] = []
            }
        }

        const award = document.querySelector(".awardInfo")
        let luckyIndex = 0
        let winnerDog = null

        document.querySelector('.draw').addEventListener('click', async (e) => {
            if (e.target.tagName === 'LI') {
                e.target.style.color = '#16a9c1'
                if (await new Modal('友情提示', '是否删除当前中奖人').open()) {
                    userArr[luckyIndex].splice(e.target.dataset.index, 1)
                    getLuckyDog(userArr[luckyIndex])
                    localStorage.setItem('user', JSON.stringify(userArr))
                } else {
                    e.target.style.color = ''
                }
            }
        })

        function renderAwardInfo() {
            award.innerHTML = ''
            let isFirstButton = true
            awardInfo.forEach((item, index) => {
                const li = document.createElement('li')
                li.textContent = ++index
                li.setAttribute('data-id', item.id)
                if (isFirstButton) {
                    li.style.backgroundColor = '#005ec2'
                    luckyIndex = item.id
                    luckyNum = item.quantity
                    isFirstButton = false
                }
                award.appendChild(li)
            })
            updateHeaderInfo(awardInfo[0])
        }

        function updateHeaderInfo(selectedAward) {
            if (selectedAward) {
                document.querySelector('.top h1').textContent = selectedAward.name ? selectedAward.name : '幸运抽奖'
                document.querySelector('.top h2').textContent = selectedAward.award
                document.querySelector('.top h3').textContent = `(${selectedAward.quantity})`
            }
        }

        document.addEventListener('DOMContentLoaded', renderAwardInfo)

        document.querySelector('.awardInfo').addEventListener('click', (e) => {
            if (e.target.tagName === "LI") {
                document.querySelectorAll('.awardInfo li').forEach(li => {
                    li.style.backgroundColor = ''
                })
                e.target.style.backgroundColor = '#005ec2'
                const selectedAward = awardInfo[e.target.getAttribute('data-id')]
                luckyIndex = selectedAward.id
                updateHeaderInfo(selectedAward)
                getLuckyDog(userArr[luckyIndex])
            }
        })

        function DogInfo(id, code, name, department, drawCode) {
            this.id = id
            this.code = code
            this.name = name
            this.department = department
            this.drawCode = drawCode
        }

        getLuckyDog(userArr[luckyIndex])

        // 初始化数字滚动器
        let numberRoller
        window.addEventListener('load', function () {
            numberRoller = new NumberRoller(document.getElementById('number-display'))
        })

        // 键盘事件处理
        window.addEventListener('keyup', (e) => {
            if (e.key === 'Enter' || e.key === 'PageDown') {
                if (controlGame && !isDrawing) {
                    if (userArr[luckyIndex].length < awardInfo[luckyIndex].quantity) {
                        startLottery()
                    }
                } else if (isDrawing) {
                    stopLottery()
                }
            }
        })

        // 开始抽奖
        function startLottery() {
            if (isDrawing) return

            isDrawing = true
            controlGame = false

            // 获取中奖者
            const result = retryGetUser()
            if (result.length === 0) {
                isDrawing = false
                controlGame = true
                return
            }

            const [{ id, code, name, department }] = result
            winnerDog = new DogInfo(id, code, name, department)
            winnerDog.drawCode = awardInfo[luckyIndex].award

            // 更新状态显示
            document.querySelector('.status-text').textContent = '抽奖中... 按 Enter 停止'
            document.getElementById('winner-info').classList.remove('show')

            // 开始数字滚动
            numberRoller.startRolling()

            // 创建粒子流效果
            const canvas = document.getElementById('particle-canvas')
            const centerX = canvas.width / 2
            const centerY = canvas.height / 2
            particleSystem.createParticleStream(centerX, centerY, 5000)
        }

        // 停止抽奖
        function stopLottery() {
            if (!isDrawing) return

            isDrawing = false
            controlGame = true

            // 停止数字滚动并显示结果
            numberRoller.stopRolling(winnerDog.code)

            // 添加中奖者到列表
            userArr[luckyIndex].push(winnerDog)
            localStorage.setItem('user', JSON.stringify(userArr))

            // 更新显示
            document.querySelector('.status-text').textContent = '恭喜中奖！按 Enter 继续抽奖'

            // 显示中奖者信息
            setTimeout(() => {
                const winnerInfo = document.getElementById('winner-info')
                winnerInfo.innerHTML = `
                    <div style="font-size: 1.2em; margin-bottom: 10px;">${winnerDog.name}</div>
                    <div style="font-size: 1em; color: #666;">${winnerDog.department}</div>
                    <div style="font-size: 0.9em; color: #999; margin-top: 5px;">${winnerDog.drawCode}</div>
                `
                winnerInfo.classList.add('show')

                // 更新中奖列表
                getLuckyDog(userArr[luckyIndex])
            }, 1000)

            // 创建庆祝效果
            setTimeout(() => {
                particleSystem.createCelebration()
            }, 1200)
        }

        function retryGetUser() {
            const result = getUser()
            if (result.length === 0) {
                console.log('没有数据，尝试重新获取...')
                return []
            } else {
                return result
            }
        }

        function getUser() {
            if (luckyDogs.length === 0) {
                alert('没有可抽奖的人员！')
                return []
            }

            let num = getRandom(0, luckyDogs.length)
            if (getDog) {
                num = 0
                getDog = false
            }

            const luckyDog = luckyDogs.splice(num, 1)
            localStorage.setItem('luckyDogs', JSON.stringify(luckyDogs))
            return luckyDog
        }

        function getRandom(start, end) {
            return Math.floor(Math.random() * (end - start) + start)
        }

        function getLuckyDog(userItem) {
            document.querySelector('.draw ul').innerHTML = userItem.sort().map((item, index) => {
                const { code, name, department, drawCode } = item
                return `
            <li data-index="${index}">${name} ${code} ${department}<span style="font-size:20px;margin-left:10px;">${drawCode}</span></li>
            `
            }).join('')
        }

        // 重置按钮功能
        function resetLottery() {
            isDrawing = false
            controlGame = true
            numberRoller.reset()
            particleSystem.clear()
            document.querySelector('.status-text').textContent = '按 Enter 开始抽奖'
            document.getElementById('winner-info').classList.remove('show')
        }

        // 添加重置快捷键
        window.addEventListener('keyup', (e) => {
            if (e.key === 'Escape') {
                resetLottery()
            }
        })
    </script>