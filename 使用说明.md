# 抽奖系统使用说明

## 快速开始

### 1. 启动后端服务
```bash
node js/index.js
```
服务将在 http://localhost:8080 启动

### 2. 设置抽奖数据
1. 打开浏览器访问 `setting.html`
2. 设置奖项信息（奖项名称、数量、奖品详情）
3. 点击"重置"按钮从Excel文件导入参与人员数据
4. 选择抽奖方式：
   - **去抽奖(老虎机)**: 原版老虎机滚动效果
   - **去抽奖(粒子特效)**: 新版粒子特效动画

## 两种抽奖模式对比

### 原版老虎机模式 (slotmachines.html)
- 使用lucky-canvas库的SlotMachine组件
- 6位数字同时滚动
- 经典老虎机视觉效果
- 操作：Enter开始，Enter停止

### 新版粒子特效模式 (particle-lottery.html)
- 自定义粒子系统
- 数字逐个停止，增加悬念
- 丰富的视觉特效：
  - 抽奖时：粒子流动画
  - 中奖时：烟花庆祝效果
  - 数字滚动：脉冲和缩放动画
- 操作：Enter开始，Enter停止，Escape重置

## 操作说明

### 通用快捷键
- **x**: 切换全屏模式
- **Alt**: 调试模式（固定抽取第一个人）
- **Enter/PageDown**: 开始/停止抽奖

### 粒子特效模式额外快捷键
- **Escape**: 重置当前抽奖状态

### 奖项切换
- 点击页面底部的数字按钮切换不同奖项
- 蓝色高亮显示当前选中的奖项

### 中奖名单管理
- 点击已中奖人员可以删除（需确认）
- 中奖数据自动保存到localStorage

## 数据格式

### Excel文件格式
参与人员Excel文件应包含以下列：
- 列A: 序号
- 列B: 工号
- 列C: 姓名  
- 列D: 部门

### 奖项设置
每个奖项包含：
- 奖项名称（如：一等奖）
- 数量（如：1）
- 奖品详情（如：500京东卡）

## 技术特点

### 粒子特效系统
- 基于Canvas的实时粒子渲染
- 物理引擎：重力、碰撞、生命周期
- 多种粒子类型：基础粒子、烟花粒子
- 性能优化：自动清理、requestAnimationFrame

### 数据持久化
- localStorage存储中奖结果
- 支持多轮抽奖
- 数据格式向后兼容

## 故障排除

### 常见问题
1. **没有参与人员数据**
   - 确保Excel文件在userInfo文件夹中
   - 点击setting.html中的"重置"按钮重新导入

2. **粒子效果不显示**
   - 检查浏览器是否支持Canvas
   - 尝试刷新页面重新加载

3. **抽奖无响应**
   - 检查控制台是否有错误信息
   - 确保localStorage中有参与人员数据

### 浏览器兼容性
- 推荐使用Chrome、Firefox、Safari、Edge最新版本
- 需要支持ES6、Canvas、localStorage

## 自定义配置

### 修改粒子效果
编辑 `js/particle-effects.js`：
- 调整粒子数量、颜色、速度
- 修改重力、碰撞参数
- 自定义动画时长

### 修改界面样式
编辑对应HTML文件的CSS部分：
- 调整颜色主题
- 修改字体大小
- 自定义动画效果

## 备份与恢复

### 备份中奖数据
```javascript
// 在浏览器控制台执行
console.log(localStorage.getItem('user'))
```

### 恢复中奖数据
```javascript
// 在浏览器控制台执行
localStorage.setItem('user', '你的备份数据')
```
