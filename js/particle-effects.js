// 粒子类
class Particle {
    constructor(x, y, color = '#FFD700') {
        this.x = x
        this.y = y
        this.vx = (Math.random() - 0.5) * 8
        this.vy = (Math.random() - 0.5) * 8
        this.life = 1.0
        this.decay = Math.random() * 0.02 + 0.01
        this.size = Math.random() * 4 + 2
        this.color = color
        this.gravity = 0.1
        this.bounce = 0.7
    }

    update(canvas) {
        this.x += this.vx
        this.y += this.vy
        this.vy += this.gravity
        this.life -= this.decay

        // 边界碰撞
        if (this.x <= 0 || this.x >= canvas.width) {
            this.vx *= -this.bounce
            this.x = Math.max(0, Math.min(canvas.width, this.x))
        }
        if (this.y >= canvas.height) {
            this.vy *= -this.bounce
            this.y = canvas.height
        }

        return this.life > 0
    }

    draw(ctx) {
        ctx.save()
        ctx.globalAlpha = this.life
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
        ctx.restore()
    }
}

// 烟花粒子类
class FireworkParticle extends Particle {
    constructor(x, y, color, angle, speed) {
        super(x, y, color)
        this.vx = Math.cos(angle) * speed
        this.vy = Math.sin(angle) * speed
        this.gravity = 0.05
        this.size = Math.random() * 3 + 1
        this.decay = Math.random() * 0.015 + 0.005
    }
}

// 粒子系统类
class ParticleSystem {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId)
        this.ctx = this.canvas.getContext('2d')
        this.particles = []
        this.isAnimating = false

        this.resizeCanvas()
        window.addEventListener('resize', () => this.resizeCanvas())

        this.animate()
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth
        this.canvas.height = window.innerHeight
    }

    // 创建爆炸效果
    createExplosion(x, y, color = '#FFD700', particleCount = 30) {
        for (let i = 0; i < particleCount; i++) {
            this.particles.push(new Particle(x, y, color))
        }
    }

    // 创建烟花效果
    createFirework(x, y, colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']) {
        const particleCount = 50
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount
            const speed = Math.random() * 6 + 2
            const color = colors[Math.floor(Math.random() * colors.length)]
            this.particles.push(new FireworkParticle(x, y, color, angle, speed))
        }
    }

    // 创建连续的粒子流
    createParticleStream(x, y, duration = 2000) {
        const colors = ['#FFD700', '#FFA500', '#FF6347', '#FF69B4', '#00CED1']
        const startTime = Date.now()

        const createBurst = () => {
            if (Date.now() - startTime < duration) {
                for (let i = 0; i < 5; i++) {
                    const offsetX = (Math.random() - 0.5) * 100
                    const offsetY = (Math.random() - 0.5) * 50
                    const color = colors[Math.floor(Math.random() * colors.length)]
                    this.particles.push(new Particle(x + offsetX, y + offsetY, color))
                }
                setTimeout(createBurst, 100)
            }
        }
        createBurst()
    }

    // 创建庆祝效果
    createCelebration() {
        const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57']

        // 多个烟花同时绽放
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                const x = Math.random() * this.canvas.width
                const y = Math.random() * this.canvas.height * 0.6 + this.canvas.height * 0.1
                this.createFirework(x, y, colors)
            }, i * 300)
        }

        // 持续的粒子雨
        for (let i = 0; i < 20; i++) {
            setTimeout(() => {
                const x = Math.random() * this.canvas.width
                const y = -10
                for (let j = 0; j < 3; j++) {
                    const particle = new Particle(x + (Math.random() - 0.5) * 50, y, colors[Math.floor(Math.random() * colors.length)])
                    particle.vy = Math.random() * 3 + 1
                    particle.vx = (Math.random() - 0.5) * 2
                    particle.gravity = 0.05
                    particle.decay = 0.005
                    this.particles.push(particle)
                }
            }, i * 100)
        }
    }

    // 清除所有粒子
    clear() {
        this.particles = []
    }

    // 动画循环
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

        // 更新和绘制粒子
        this.particles = this.particles.filter(particle => {
            const alive = particle.update(this.canvas)
            if (alive) {
                particle.draw(this.ctx)
            }
            return alive
        })

        requestAnimationFrame(() => this.animate())
    }

    // 获取粒子数量
    getParticleCount() {
        return this.particles.length
    }
}

// 数字滚动动画类
class NumberRoller {
    constructor(displayElement) {
        this.displayElement = displayElement
        this.currentNumbers = ['0', '0', '0', '0', '0', '0']
        this.targetNumbers = ['0', '0', '0', '0', '0', '0']
        this.isRolling = false
        this.rollingSpeeds = [0, 0, 0, 0, 0, 0]
        this.rollingIntervals = []
    }

    // 开始滚动
    startRolling() {
        if (this.isRolling) return

        this.isRolling = true
        this.displayElement.classList.add('pulse')

        // 为每个数字位设置不同的滚动速度
        for (let i = 0; i < 6; i++) {
            this.rollingSpeeds[i] = Math.random() * 50 + 30 // 30-80ms
            this.rollingIntervals[i] = setInterval(() => {
                this.currentNumbers[i] = Math.floor(Math.random() * 10).toString()
                this.updateDisplay()
            }, this.rollingSpeeds[i])
        }
    }

    // 停止滚动并显示最终结果
    stopRolling(finalNumber) {
        if (!this.isRolling) return

        this.targetNumbers = finalNumber.toString().padStart(6, '0').split('')

        // 逐个停止数字滚动，创建延迟效果
        for (let i = 0; i < 6; i++) {
            setTimeout(() => {
                clearInterval(this.rollingIntervals[i])
                this.currentNumbers[i] = this.targetNumbers[i]
                this.updateDisplay()

                // 最后一个数字停止时
                if (i === 5) {
                    this.isRolling = false
                    this.displayElement.classList.remove('pulse')
                    this.displayElement.classList.add('firework')
                    setTimeout(() => {
                        this.displayElement.classList.remove('firework')
                    }, 500)
                }
            }, i * 200) // 每个数字延迟200ms停止
        }
    }

    // 更新显示
    updateDisplay() {
        this.displayElement.textContent = this.currentNumbers.join('')
    }

    // 重置
    reset() {
        this.rollingIntervals.forEach(interval => clearInterval(interval))
        this.rollingIntervals = []
        this.isRolling = false
        this.currentNumbers = ['0', '0', '0', '0', '0', '0']
        this.displayElement.classList.remove('pulse', 'firework')
        this.displayElement.textContent = '000000'
    }
}
